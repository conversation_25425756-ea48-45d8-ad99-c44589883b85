import adapter from '@sveltejs/adapter-vercel';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://svelte.dev/docs/kit/integrations
	// for more information about preprocessors
	preprocess: vitePreprocess(),
	kit: { 
		adapter: adapter(),
		files: {
			hooks:
				{
						server: 'src/hooks/hooks.server.ts'
				}
		}
	 },
	csp: {
	mode: 'auto',
	directives: {
		'script-src': ["'self'", 'https://www.googletagmanager.com'],
		'script-src-elem': ["'self'", 'https://www.googletagmanager.com'],
		'object-src': ["'self'"],
		'base-uri': ["'self'"]
		}
	},
};

export default config;
