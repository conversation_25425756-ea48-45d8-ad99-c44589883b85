import { fail, redirect, error } from '@sveltejs/kit';
import { newTaskSchema } from '$lib/schemas';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { insertTask, fetchTasks } from '$lib/supabaseClient';
import type { ServerLoad } from '@sveltejs/kit';

export const load: ServerLoad = async (event) => {
    const { data: userData, error: userError } = await event.locals.supabase.auth.getUser();

    let tasks = [];
    if (userData && userData.user) {
        tasks = await fetchTasks(userData.user.id);
    }
    console.log('Tasks:', tasks);
    const form = await superValidate(event, zod(newTaskSchema));
    return { form, tasks };
};

export const actions = {
    default: async ({request, locals}) => {
        const data = await request.formData();
        const { data: userData, error: userError } = await locals.supabase.auth.getUser();
        const userId = userData?.user?.id;
        if (!userId) {
            throw error(401, 'Unauthorized');
        }
        const form = await superValidate(data, zod(newTaskSchema));

        if (!form.valid) {
            return fail(400, { form });
        }

        try {
            await insertTask(form.data.task_name, form.data.task_description, userId);
            // Fetch updated tasks after insertion
            const updatedTasks = await fetchTasks(userId);
            // Optionally, reset the form after successful insert
            const resetForm = await superValidate({}, zod(newTaskSchema));
            return { tasks: updatedTasks, form: resetForm };
        } catch (error) {
            console.error('Error inserting task:', error);
            return fail(500, { form });
        }
    }
}