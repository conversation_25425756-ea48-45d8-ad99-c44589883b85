import { z } from 'zod';

export const loginSchema = z.object({
	identifier: z.string().email({ message: 'E-postadressen er ugyldig' }),
	password: z.string().min(10, { message: 'Passordet må være minst 10 tegn' })
});

export const newTaskSchema = z.object({
	task_name: z.string().min(1, { message: 'Tittel må fylles ut' }),
	task_description: z.string().min(1, { message: '<PERSON>skrivelse må fylles ut' }),
});
