<script lang="ts">
    import { zod } from 'sveltekit-superforms/adapters';
	import { superForm } from 'sveltekit-superforms/client';
	import type { PageData } from './$types';
	import { newTaskSchema } from '$lib/schemas';
    export let data: PageData;
    let tasks = data.tasks || [];

    $: tasks = data.tasks || [];
    let show_form = false;

	const { form, errors, enhance, constraints } = superForm(data.form, {
		taintedMessage:
			'Er du sikker på at du vil forlate siden? Alle endringer du har gjort vil gå tapt.',
		validators: zod(newTaskSchema)
	});
    // Use the correct enhance signature for superForm
    const customEnhance = (formEl: HTMLFormElement) =>
        enhance(formEl, {
            onResult: (event) => {
                if (event.result?.type === 'success') {
                    show_form = false;
                }
            }
        });
</script>

<section id="main">
    <h1>Welcome to WebTask</h1>
    <p>Your one-stop solution for task management.</p>
    <div class="tasks-section">
        <h2>Your Tasks</h2>
        <ul class="task-list">
            {#if tasks.length > 0}
                {#each tasks as task}
                    <li class="task-item">
                        <h3>{task.task_name}</h3>
                        <p>{task.task_description}</p>
                    </li>
                {/each}
            {:else}
                <p>No tasks available. Start by adding a new task!</p>
            {/if}
            <li class="add-task">
                <button on:click={() => show_form = !show_form}>+ Add new task</button>
            </li>
        </ul>
    </div>

    {#if show_form}
        <form method="post" use:customEnhance class="task-form">
            <div class="input-container">
                <label for="title">Task Name</label>
                <input
                    type="text"
                    id="task_name"
                    name="task_name"
                    placeholder="Enter task name"
                    required
                    bind:value={$form.task_name}
                >
            </div>
            <div class="input-container">
                <label for="description">Task Description</label>
                <textarea
                    id="task_description"
                    name="task_description"
                    placeholder="Enter task description"
                    required
                    bind:value={$form.task_description}
                ></textarea>
            </div>
            <button class="submit-button" type="submit">Submit</button>
        </form>
    {/if}

</section>

<style>
    #main {
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    h2 {
        margin-top: 2rem;
        display: flex;
        justify-content: center;
    }
    .task-list {
        display: grid;
        grid-template-columns: 300px 300px;
        gap: 1rem;
        width: 100%;
    }
    .task-item {
        border: 1px solid #ccc;
        border-radius: 8px;
        padding: 1rem;
        background-color: #f9f9f9;
    }
    form {
        margin-top: 2rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        width: 300px;
    }
    .input-container {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
</style>