import pkg from '@supabase/supabase-js';
import type { SupabaseClient } from '@supabase/supabase-js';

import { config } from 'dotenv';
const { createClient } = pkg;

config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
console.log(supabaseUrl, supabaseKey)

if (!supabaseKey || !supabaseUrl) {
  throw new Error('Missing SUPABASE environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseKey, {
});


export async function loginUser(email: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      if (error) throw error
      return data;
    } 
    catch (error) {
        if (error instanceof Error) {
            console.error('Error logging in:', error.message);
        } else {
            console.error('Unexpected error logging in:', error);
        }
    };
}

export async function insertTask(task_name: string, task_description: string, user_id: string) { 
  const { data, error } = await supabase.from('tasks').insert({
    task_name: task_name,
    task_description: task_description,
    user_id: user_id
  });
  if (error) {
    console.error('Error:', error);
    return null;
  }

  return data;

}

export async function fetchTasks(userId: string) {
	const { data, error } = await supabase
		.from('tasks')
		.select('*')
		.eq('user_id', userId)
	if (error) {
		console.error('Error fetching tasks:', error);
		return [];
	}
  console.log('Fetched tasks:', data);

	return data;
}

